import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface Product {
  id: number;
  name: string;
  price: number;
  image_url: string;
  business: {
    name: string;
    slug: string;
  };
}

export function FeaturedProducts() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchFeaturedProducts() {
      try {
        // For now, we'll create mock data since the API might not have products endpoint
        // In a real implementation, this would be: const response = await api.getFeaturedProducts();
        const mockProducts: Product[] = [
          {
            id: 1,
            name: "Artisan Coffee Blend",
            price: 24.99,
            image_url: "/product-coffee.jpg",
            business: { name: "Joe's Coffee Shop", slug: "joes-coffee-shop" }
          },
          {
            id: 2,
            name: "Handcrafted Leather Wallet",
            price: 89.99,
            image_url: "/product-wallet.jpg",
            business: { name: "Bella's Boutique", slug: "bellas-boutique" }
          },
          {
            id: 3,
            name: "Organic Honey",
            price: 15.99,
            image_url: "/product-honey.jpg",
            business: { name: "Green Thumb Farm", slug: "green-thumb-farm" }
          },
          {
            id: 4,
            name: "Custom Phone Case",
            price: 34.99,
            image_url: "/product-phone-case.jpg",
            business: { name: "Tech Solutions Inc", slug: "tech-solutions-inc" }
          },
          {
            id: 5,
            name: "Gourmet Chocolate Box",
            price: 42.99,
            image_url: "/product-chocolate.jpg",
            business: { name: "Sweet Treats", slug: "sweet-treats" }
          },
          {
            id: 6,
            name: "Handmade Soap Set",
            price: 28.99,
            image_url: "/product-soap.jpg",
            business: { name: "Natural Beauty Co", slug: "natural-beauty-co" }
          }
        ];
        
        setProducts(mockProducts);
      } catch (err) {
        setError('Failed to load featured products');
        console.error('Error fetching featured products:', err);
      } finally {
        setLoading(false);
      }
    }

    fetchFeaturedProducts();
  }, []);

  if (loading) {
    return (
      <section className="py-16 sm:py-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-12 text-center">
            <h2 className="h2 text-3xl font-bold tracking-tight sm:text-4xl">
              Featured Products
            </h2>
            <p className="mt-4 text-lg text-muted-foreground">
              Discover top products from our local businesses.
            </p>
          </div>
          <div className="flex snap-x snap-mandatory gap-6 overflow-x-auto pb-8 scrollbar-hide">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="flex-shrink-0 snap-center">
                <Card className="w-80 animate-pulse">
                  <div className="h-56 bg-muted"></div>
                  <CardContent className="p-4">
                    <div className="h-5 bg-muted rounded mb-2"></div>
                    <div className="h-4 bg-muted rounded mb-2"></div>
                    <div className="h-6 bg-muted rounded w-20"></div>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-16 sm:py-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-muted-foreground">{error}</p>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 sm:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-12 text-center">
          <h2 className="h2 text-3xl font-bold tracking-tight sm:text-4xl">
            Featured Products
          </h2>
          <p className="mt-4 text-lg text-muted-foreground">
            Discover top products from our local businesses.
          </p>
        </div>

        <div className="relative">
          <div className="flex snap-x snap-mandatory gap-6 overflow-x-auto pb-8 scrollbar-hide">
            {products.map((product) => (
              <div key={product.id} className="flex-shrink-0 snap-center">
                <Card className="w-80 overflow-hidden bg-card shadow-lg transition-all hover:shadow-xl">
                  <div
                    className="h-56 w-full bg-cover bg-center"
                    style={{ 
                      backgroundImage: `url('${product.image_url}')`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center'
                    }}
                  />
                  <CardContent className="p-4">
                    <h3 className="text-lg font-semibold text-foreground">
                      {product.name}
                    </h3>
                    <p className="mt-1 text-sm text-muted-foreground">
                      from{' '}
                      <Link 
                        to={`/business/${product.business.slug}`}
                        className="hover:text-primary transition-colors"
                      >
                        {product.business.name}
                      </Link>
                    </p>
                    <p className="mt-2 text-lg font-bold text-primary">
                      ${product.price.toFixed(2)}
                    </p>
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>

        {products.length > 0 && (
          <div className="text-center mt-8">
            <Button variant="outline" asChild>
              <Link to="/products">View All Products</Link>
            </Button>
          </div>
        )}
      </div>
    </section>
  );
}

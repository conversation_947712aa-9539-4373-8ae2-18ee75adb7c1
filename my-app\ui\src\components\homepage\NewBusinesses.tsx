import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { api } from '@/lib/serverComm';
import { Star, MapPin, Calendar } from 'lucide-react';

interface Business {
  id: number;
  name: string;
  slug: string;
  short_description: string;
  address: string;
  logo_url: string;
  hero_image_url: string;
  average_rating: string;
  total_reviews: number;
  created_at: string;
  category: {
    id: number;
    name: string;
    slug: string;
    icon: string;
  };
}

export function NewBusinesses() {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchNewBusinesses() {
      try {
        const response = await api.getBusinesses({ 
          limit: 4 
        });
        // The API returns businesses sorted by creation date (newest first)
        setBusinesses(response.businesses);
      } catch (err) {
        setError('Failed to load new businesses');
        console.error('Error fetching new businesses:', err);
      } finally {
        setLoading(false);
      }
    }

    fetchNewBusinesses();
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <section className="py-16 sm:py-24 bg-secondary/20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="h2 text-3xl font-bold tracking-tight sm:text-4xl">New Businesses</h2>
            <p className="mt-4 text-lg text-muted-foreground">Welcome our newest community members</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <div className="aspect-video bg-muted"></div>
                <CardContent className="p-6">
                  <div className="h-5 bg-muted rounded mb-3"></div>
                  <div className="h-4 bg-muted rounded mb-3"></div>
                  <div className="h-4 bg-muted rounded w-24"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-16 sm:py-24 bg-secondary/20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <p className="text-muted-foreground">{error}</p>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 sm:py-24 bg-secondary/20">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-12 text-center">
          <h2 className="h2 text-3xl font-bold tracking-tight sm:text-4xl">
            New Businesses
          </h2>
          <p className="mt-4 text-lg text-muted-foreground max-w-2xl mx-auto">
            Welcome our newest community members! Discover fresh businesses
            that have recently joined our directory.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {businesses.map((business) => (
            <Link key={business.id} to={`/business/${business.slug}`} className="group">
              <Card className="h-full overflow-hidden transition-all duration-200 hover:shadow-lg hover:-translate-y-1">
                {/* Business Image with aspect-video ratio */}
                <div className="relative aspect-video overflow-hidden">
                  {business.hero_image_url ? (
                    <img
                      src={business.hero_image_url}
                      alt={business.name}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                      <span className="text-2xl font-bold text-muted-foreground">
                        {business.name.charAt(0)}
                      </span>
                    </div>
                  )}
                  <Badge className="absolute top-3 left-3 bg-green-600 text-xs font-medium">
                    New
                  </Badge>
                  {business.logo_url && (
                    <div className="absolute bottom-3 right-3 w-10 h-10 rounded-full overflow-hidden border-2 border-white shadow-lg">
                      <img
                        src={business.logo_url}
                        alt={`${business.name} logo`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                </div>

                <CardContent className="p-6">
                  <div className="space-y-3">
                    {/* Business Name */}
                    <h3 className="text-lg font-semibold group-hover:text-primary transition-colors line-clamp-1">
                      {business.name}
                    </h3>

                    {/* Category */}
                    <Badge variant="outline" className="text-xs">
                      {business.category.name}
                    </Badge>

                    {/* Description */}
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {business.short_description}
                    </p>

                    {/* Rating */}
                    {business.total_reviews > 0 && (
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-sm font-medium">
                          {parseFloat(business.average_rating).toFixed(1)}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          ({business.total_reviews} reviews)
                        </span>
                      </div>
                    )}

                    {/* Location & Date */}
                    <div className="space-y-1 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <MapPin className="w-4 h-4" />
                        <span className="line-clamp-1">{business.address}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        <span>Joined {formatDate(business.created_at)}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {businesses.length > 0 && (
          <div className="text-center mt-8">
            <Button variant="outline" asChild>
              <Link to="/businesses">Explore All Businesses</Link>
            </Button>
          </div>
        )}
      </div>
    </section>
  );
}

import { useState } from 'react';
import { Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

interface HeroSectionProps {
  onSearch?: (query: string) => void;
}

export function HeroSection({ onSearch }: HeroSectionProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();

  const handleSearch = () => {
    if (searchQuery.trim()) {
      if (onSearch) {
        onSearch(searchQuery);
      } else {
        // Navigate to search page with query parameters
        const params = new URLSearchParams();
        params.set('q', searchQuery);
        navigate(`/search?${params.toString()}`);
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <section
      className="relative flex min-h-[60vh] items-center justify-center bg-gradient-to-br from-primary/20 via-background to-secondary/20 bg-cover bg-center py-20 text-center"
      style={{
        backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6)), url('/hero-business-bg.jpg')`
      }}
    >
      <div className="container mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
        <h1 className="h1 font-serif text-foreground dark:text-white text-4xl sm:text-5xl md:text-6xl font-extrabold tracking-tighter">
          Discover the Best Businesses in Your Community
        </h1>
        <p className="mt-4 max-w-2xl mx-auto text-lg text-muted-foreground dark:text-slate-200">
          Explore a curated directory of local businesses, from cozy cafes to expert services, all in one place.
        </p>

        {/* Large Search Bar */}
        <div className="mx-auto mt-8 flex w-full max-w-2xl items-center">
          <div className="relative w-full">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4">
              <Search className="h-6 w-6 text-gray-400" />
            </div>
            <input
              className="h-16 w-full rounded-lg border border-transparent bg-white/90 pl-12 pr-32 text-lg text-foreground placeholder:text-muted-foreground focus:border-primary focus:ring-primary"
              placeholder="Search for businesses or services"
              type="search"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
            />
            <Button
              onClick={handleSearch}
              className="absolute inset-y-0 right-0 m-2 px-6 text-sm font-semibold"
            >
              Search
            </Button>
          </div>
        </div>

      </div>
    </section>
  );
}

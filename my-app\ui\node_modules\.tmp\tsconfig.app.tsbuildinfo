{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/appsidebar.tsx", "../../src/components/login-form.tsx", "../../src/components/mode-toggle.tsx", "../../src/components/navbar.tsx", "../../src/components/theme-provider.tsx", "../../src/components/application/applicationsuccess.tsx", "../../src/components/application/businessapplicationform.tsx", "../../src/components/business/businesscard.tsx", "../../src/components/business/businessheader.tsx", "../../src/components/business/businesshours.tsx", "../../src/components/business/businessprofile.tsx", "../../src/components/business/contactinfo.tsx", "../../src/components/business/photogallery.tsx", "../../src/components/business/reviewssection.tsx", "../../src/components/homepage/categoryhighlights.tsx", "../../src/components/homepage/featuredbusinesses.tsx", "../../src/components/homepage/featuredproducts.tsx", "../../src/components/homepage/herosection.tsx", "../../src/components/homepage/marqueebanner.tsx", "../../src/components/homepage/newbusinesses.tsx", "../../src/components/navigation/mobilenav.tsx", "../../src/components/navigation/publicheader.tsx", "../../src/components/search/filterpanel.tsx", "../../src/components/search/searchbar.tsx", "../../src/components/search/searchresults.tsx", "../../src/components/seo/seohead.tsx", "../../src/components/theme/modetogglebutton.tsx", "../../src/components/theme/modetoggledropdown.tsx", "../../src/components/theme/modetoggleswitch.tsx", "../../src/components/theme/themeselector.tsx", "../../src/components/theme/themesettings.tsx", "../../src/components/theme/themeshowcase.tsx", "../../src/components/theme/index.ts", "../../src/components/ui/avatar.tsx", "../../src/components/ui/badge.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/checkbox.tsx", "../../src/components/ui/dialog.tsx", "../../src/components/ui/dropdown-menu.tsx", "../../src/components/ui/input.tsx", "../../src/components/ui/label.tsx", "../../src/components/ui/lazy-image.tsx", "../../src/components/ui/navigation-menu.tsx", "../../src/components/ui/navigation.tsx", "../../src/components/ui/select.tsx", "../../src/components/ui/separator.tsx", "../../src/components/ui/sheet.tsx", "../../src/components/ui/sidebar.tsx", "../../src/components/ui/skeleton.tsx", "../../src/components/ui/switch.tsx", "../../src/components/ui/textarea.tsx", "../../src/components/ui/tooltip.tsx", "../../src/components/ui/virtual-list.tsx", "../../src/hooks/use-mobile.ts", "../../src/hooks/usedebounce.ts", "../../src/hooks/usethemepreference.ts", "../../src/lib/auth-context.tsx", "../../src/lib/design-system.ts", "../../src/lib/firebase.ts", "../../src/lib/servercomm.ts", "../../src/lib/theme-utils.ts", "../../src/lib/utils.ts", "../../src/pages/applicationpage.tsx", "../../src/pages/businessprofilepage.tsx", "../../src/pages/categoriespage.tsx", "../../src/pages/categorydetailpage.tsx", "../../src/pages/home.tsx", "../../src/pages/homepage.tsx", "../../src/pages/page1.tsx", "../../src/pages/page2.tsx", "../../src/pages/searchpage.tsx", "../../src/pages/settings.tsx", "../../src/pages/admin/admindashboard.tsx"], "errors": true, "version": "5.8.3"}
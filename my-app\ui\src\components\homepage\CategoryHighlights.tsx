import { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { api } from '@/lib/serverComm';
import { 
  Store, 
  Utensils, 
  Car, 
  Heart, 
  Briefcase, 
  Home,
  Scissors,
  Dumbbell,
  GraduationCap,
  Wrench
} from 'lucide-react';

interface Category {
  id: number;
  name: string;
  slug: string;
  icon: string;
  description: string;
  business_count: number;
}

const iconMap: Record<string, React.ComponentType<any>> = {
  store: Store,
  utensils: Utensils,
  car: Car,
  heart: Heart,
  briefcase: Briefcase,
  home: Home,
  scissors: Scissors,
  dumbbell: Dumbbell,
  'graduation-cap': GraduationCap,
  wrench: Wrench,
};

export function CategoryHighlights() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchCategories() {
      try {
        const response = await api.getCategories();
        // Show top 8 categories with most businesses
        const sortedCategories = response.categories
          .sort((a: Category, b: Category) => b.business_count - a.business_count)
          .slice(0, 8);
        setCategories(sortedCategories);
      } catch (err) {
        setError('Failed to load categories');
        console.error('Error fetching categories:', err);
      } finally {
        setLoading(false);
      }
    }

    fetchCategories();
  }, []);

  if (loading) {
    return (
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Browse Categories</h2>
            <p className="text-muted-foreground">Explore businesses by category</p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 bg-muted rounded-full mx-auto mb-4"></div>
                  <div className="h-4 bg-muted rounded mb-2"></div>
                  <div className="h-3 bg-muted rounded w-16 mx-auto"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-16 px-4">
        <div className="container mx-auto text-center">
          <p className="text-muted-foreground">{error}</p>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 sm:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-12 text-center">
          <h2 className="h2 text-3xl font-bold tracking-tight sm:text-4xl">
            Explore Categories
          </h2>
          <p className="mt-4 text-lg text-muted-foreground">
            Find what you're looking for by browsing our popular categories.
          </p>
        </div>

        <div className="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
          {categories.map((category) => {
            return (
              <Link
                key={category.id}
                to={`/categories/${category.slug}`}
                className="group flex flex-col items-center gap-3 rounded-lg bg-card p-4 text-center transition-all hover:shadow-lg hover:-translate-y-1"
              >
                <div
                  className="h-24 w-24 rounded-full bg-cover bg-center"
                  style={{
                    backgroundImage: `url('${category.image_url || '/category-placeholder.jpg'}')`
                  }}
                />
                <p className="text-base font-semibold text-foreground">
                  {category.name}
                </p>
                <Badge variant="secondary" className="text-xs">
                  {category.business_count} businesses
                </Badge>
              </Link>
            );
          })}
        </div>

        <div className="text-center mt-8">
          <Link 
            to="/categories" 
            className="inline-flex items-center text-primary hover:text-primary/80 font-medium"
          >
            View All Categories →
          </Link>
        </div>
      </div>
    </section>
  );
}

import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { MobileNav } from './MobileNav';
import { ModeToggle } from '@/components/mode-toggle';
import { Building, Search, Grid3X3 } from 'lucide-react';

export function PublicHeader() {
  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/20 bg-background/80 backdrop-blur-lg transition-all duration-200">
      <div className="container mx-auto flex h-20 items-center justify-between px-4 sm:px-6 lg:px-8">
          {/* Logo */}
          <Link to="/" className="flex items-center gap-3 text-foreground">
            <div className="h-8 w-8 text-primary">
              <Building className="h-8 w-8" />
            </div>
            <h1 className="text-2xl font-bold leading-tight tracking-tighter">
              <span className="hidden sm:inline">Volo Business Directory</span>
              <span className="sm:hidden">Volo</span>
            </h1>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center gap-6">
            <Link 
              to="/" 
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Home
            </Link>
            <Link 
              to="/search" 
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Search
            </Link>
            <Link 
              to="/categories" 
              className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
            >
              Categories
            </Link>
          </nav>

          {/* Actions */}
          <div className="flex items-center gap-3">
            {/* Desktop Actions */}
            <div className="hidden md:flex items-center gap-3">
              <Button variant="ghost" size="sm" asChild>
                <Link to="/search">
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </Link>
              </Button>
              <Button size="sm" asChild>
                <Link to="/apply">List Business</Link>
              </Button>
              <ModeToggle />
            </div>

            {/* Mobile Actions */}
            <div className="md:hidden flex items-center gap-2">
              <Button variant="ghost" size="icon" asChild>
                <Link to="/search">
                  <Search className="h-5 w-5" />
                  <span className="sr-only">Search</span>
                </Link>
              </Button>
              <Button variant="ghost" size="icon" asChild>
                <Link to="/categories">
                  <Grid3X3 className="h-5 w-5" />
                  <span className="sr-only">Categories</span>
                </Link>
              </Button>
              <ModeToggle />
            </div>

            {/* Mobile Menu */}
            <MobileNav />
          </div>
      </div>
    </header>
  );
}

import { useEffect, useState } from 'react';
import { api } from '@/lib/serverComm';

interface Business {
  id: number;
  name: string;
  category: {
    name: string;
  };
}

export function MarqueeBanner() {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [announcements, setAnnouncements] = useState<string[]>([]);

  useEffect(() => {
    async function fetchBusinesses() {
      try {
        const response = await api.getBusinesses({ limit: 20 });
        setBusinesses(response.businesses);
      } catch (err) {
        console.error('Error fetching businesses for marquee:', err);
        // Fallback to static data if API fails
        setBusinesses([
          { id: 1, name: "Joe's Coffee Shop", category: { name: "Restaurant" } },
          { id: 2, name: "Tech Solutions Inc", category: { name: "Technology" } },
          { id: 3, name: "Green Thumb Landscaping", category: { name: "Home & Garden" } },
          { id: 4, name: "Bella's Boutique", category: { name: "Fashion" } },
          { id: 5, name: "Quick Fix Auto", category: { name: "Automotive" } },
        ]);
      }
    }

    fetchBusinesses();
  }, []);

  useEffect(() => {
    // Generate dynamic announcements
    const generateAnnouncements = () => {
      const newBusinesses = businesses.slice(0, 3);
      const offers = [
        "20% off at Urban Threads!",
        "Free consultation at Wellness Spa!",
        "Grand opening special at Tech Hub!",
        "Happy hour deals at Local Bistro!",
        "New arrivals at Fashion Forward!"
      ];

      const announcements = [
        ...newBusinesses.map(b => `New: ${b.name} now open!`),
        ...offers.map(o => `Offer: ${o}`),
        "Featured: Local artisan showcase this weekend!",
        "Special: Support local businesses month!",
        "Event: Community market every Saturday!"
      ];

      return [...announcements, ...announcements]; // Duplicate for seamless loop
    };

    if (businesses.length > 0) {
      setAnnouncements(generateAnnouncements());
    }
  }, [businesses]);

  return (
    <div className="overflow-hidden bg-primary py-3 text-primary-foreground">
      <div className="relative">
        <div className="marquee whitespace-nowrap">
          {announcements.map((announcement, index) => (
            <span
              key={index}
              className="mx-8 text-sm font-medium"
            >
              {announcement}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
}

// Add the marquee animation to your global CSS (index.css)
// @keyframes marquee {
//   0% {
//     transform: translateX(0%);
//   }
//   100% {
//     transform: translateX(-50%);
//   }
// }
// 
// .animate-marquee {
//   animation: marquee 30s linear infinite;
// }
